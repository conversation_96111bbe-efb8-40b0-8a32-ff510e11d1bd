<template>
	<view class="min-h-screen bg-gradient-to-b">
		<!-- Header -->
		<view class="header-sticky">
			<view v-if="$vhFrom == '' || $vhFrom == 'next'" class="header-nav">
				<vh-navbar back-icon-color="#333" title="我的余额" title-size="36" :title-bold="true" title-color="#333" />
			</view>
		</view>

		<!-- Main Content -->
		<view class="main-content">
			<view class="content-container">
				<!-- Hero Section -->
				<view class="hero-section">
					<!-- Main Icon with Enhanced Animation -->
					<view class="icon-container">
						<view class="icon-wrapper">
							<!-- Outer glow ring -->
							<view class="glow-ring"></view>

							<!-- Main icon container -->
							<view class="main-icon-container">
								<view class="icon-content">
									<image class="main-icon" :src="ossIcon('/payment/balance_icon.png')" mode="aspectFit" />
									<view class="corner-badge">
										<image class="badge-icon" :src="ossIcon('/payment/card_icon.png')" mode="aspectFit" />
									</view>
								</view>
							</view>

							<!-- Animated rings -->
							<view class="ping-ring-1"></view>
							<view class="ping-ring-2"></view>
						</view>
					</view>

					<!-- Title Section -->
					<view class="title-section">
						<view class="status-badge">
							<image class="badge-icon" :src="ossIcon('/comm/clock_icon.png')" mode="aspectFit" />
							<text class="badge-text">敬请期待</text>
							<image class="sparkle-icon" :src="ossIcon('/comm/sparkle_icon.png')" mode="aspectFit" />
						</view>
					</view>
				</view>

				<!-- Main Message Card -->
				<view class="message-card-container">
					<view class="message-card-bg"></view>
					<view class="message-card">
						<view class="card-content">
							<view class="card-divider"></view>
							<view class="card-title">
								余额/充值卡（礼品卡）
								<text class="title-break">功能即将上线</text>
							</view>
							<view class="card-subtitle">我们正在努力为您带来更好的服务体验</view>
						</view>
					</view>
				</view>

				<!-- Features Grid -->
				<view class="features-section">
					<view class="features-title">即将推出的功能</view>

					<view class="features-grid">
						<view
							v-for="(feature, index) in features"
							:key="index"
							class="feature-item"
						>
							<view class="feature-content">
								<view class="feature-icon">
									<view class="feature-dot"></view>
								</view>
								<view class="feature-text">
									<view class="feature-name">{{feature.title}}</view>
									<view class="feature-desc">{{feature.desc}}</view>
								</view>
							</view>
						</view>
					</view>
				</view>

				<!-- Footer -->
				<view class="footer-section">
					<view class="footer-divider"></view>
					<view class="footer-text">感谢您的耐心等待，精彩功能即将与您见面</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import { mapState } from 'vuex'
	export default{
		name: 'my-balance',

		data(){
			return{
				loading: false,
				features: [
					{ title: "余额充值"},
					{ title: "赠送礼品卡" },
					{ title: "便捷余额支付" }
				]
			}
		},

		computed: {
			...mapState(['routeTable'])
		},
		onLoad() {
			uni.setNavigationBarTitle({
				title: '我的余额'
			})
		},

		methods: {
			// 可以在这里添加未来的功能方法
		}
	}
</script>

<style>
@import '@/common/css/comm.css';

/* Main Layout */
.min-h-screen {
  min-height: 100vh;
}

.bg-gradient-to-b {
  background: linear-gradient(to bottom, #f9fafb 0%, rgba(254, 242, 242, 0.3) 50%, #ffffff 100%);
}

/* Header */
.header-sticky {
  position: sticky;
  top: 0;
  z-index: 10;
}

.header-nav {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(12rpx);
  border-bottom: 1rpx solid #f3f4f6;
}

/* Main Content */
.main-content {
  padding: 32rpx 32rpx 64rpx;
}

.content-container {
  max-width: 750rpx;
  margin: 0 auto;
}

/* Hero Section */
.hero-section {
  text-align: center;
  margin-bottom: 48rpx;
}

.icon-container {
  display: flex;
  justify-content: center;
  margin-bottom: 32rpx;
}

.icon-wrapper {
  position: relative;
  width: 224rpx;
  height: 224rpx;
}

/* Outer glow ring */
.glow-ring {
  position: absolute;
  inset: 0;
  width: 256rpx;
  height: 256rpx;
  background: linear-gradient(to right, rgba(202, 16, 27, 0.2), rgba(239, 68, 68, 0.2));
  border-radius: 50%;
  filter: blur(48rpx);
  left: -32rpx;
  top: -32rpx;
}

/* Main icon container */
.main-icon-container {
  position: relative;
  width: 224rpx;
  height: 224rpx;
  background: linear-gradient(135deg, #CA101B 0%, #dc2626 50%, #b91c1c 100%);
  border-radius: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 50rpx 100rpx rgba(239, 68, 68, 0.25);
}

.icon-content {
  position: relative;
}

.main-icon {
  width: 96rpx;
  height: 96rpx;
  filter: brightness(0) invert(1) drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
}

.corner-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  width: 48rpx;
  height: 48rpx;
  background: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.badge-icon {
  width: 24rpx;
  height: 24rpx;
  filter: brightness(0) saturate(100%) invert(8%) sepia(96%) saturate(7471%) hue-rotate(357deg) brightness(93%) contrast(118%);
}

/* Animated rings */
.ping-ring-1 {
  position: absolute;
  inset: 0;
  width: 224rpx;
  height: 224rpx;
  border-radius: 48rpx;
  border: 4rpx solid rgba(202, 16, 27, 0.3);
  animation: ping 2s cubic-bezier(0, 0, 0.2, 1) infinite;
}

.ping-ring-2 {
  position: absolute;
  inset: 0;
  width: 224rpx;
  height: 224rpx;
  border-radius: 48rpx;
  border: 2rpx solid rgba(202, 16, 27, 0.2);
  animation: ping 2s cubic-bezier(0, 0, 0.2, 1) infinite;
  animation-delay: 1s;
}

@keyframes ping {
  75%, 100% {
    transform: scale(1.1);
    opacity: 0;
  }
}

/* Title Section */
.title-section {
  margin-bottom: 24rpx;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 12rpx 24rpx;
  background: rgba(202, 16, 27, 0.1);
  border-radius: 50rpx;
  border: 1rpx solid rgba(202, 16, 27, 0.2);
}

.badge-icon,
.sparkle-icon {
  width: 32rpx;
  height: 32rpx;
}

.badge-icon {
  margin-right: 16rpx;
}

.sparkle-icon {
  margin-left: 16rpx;
}

.badge-text {
  font-size: 28rpx;
  font-weight: 500;
  color: #CA101B;
}

/* Message Card */
.message-card-container {
  position: relative;
  margin-bottom: 48rpx;
}

.message-card-bg {
  position: absolute;
  inset: 0;
  background: linear-gradient(to right, rgba(202, 16, 27, 0.05), rgba(239, 68, 68, 0.05));
  border-radius: 32rpx;
  filter: blur(6rpx);
}

.message-card {
  position: relative;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(8rpx);
  border-radius: 32rpx;
  padding: 40rpx;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid #f3f4f6;
}

.card-content {
  text-align: center;
}

.card-divider {
  width: 80rpx;
  height: 6rpx;
  background: linear-gradient(to right, #CA101B, #ef4444);
  margin: 0 auto 24rpx;
  border-radius: 3rpx;
}

.card-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #1f2937;
  line-height: 1.3;
  margin-bottom: 20rpx;
}

.title-break {
  display: block;
}

.card-subtitle {
  font-size: 26rpx;
  color: #6b7280;
  line-height: 1.4;
}

/* Features Section */
.features-section {
  margin-bottom: 40rpx;
}

.features-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  text-align: center;
  margin-bottom: 32rpx;
}

.features-grid {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.feature-item {
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(8rpx);
  border-radius: 24rpx;
  padding: 28rpx 32rpx;
  border: 1rpx solid #f3f4f6;
  transition: all 0.3s ease;
}

.feature-item:hover {
  border-color: rgba(202, 16, 27, 0.2);
  box-shadow: 0 20rpx 40rpx rgba(239, 68, 68, 0.1);
}

.feature-content {
  display: flex;
  align-items: center;
}

.feature-icon {
  flex-shrink: 0;
  margin-right: 24rpx;
}

.feature-dot {
  width: 24rpx;
  height: 24rpx;
  background: linear-gradient(to right, #CA101B, #ef4444);
  border-radius: 50%;
  transition: transform 0.3s ease;
}

.feature-item:hover .feature-dot {
  transform: scale(1.1);
}

.feature-text {
  flex: 1;
  min-width: 0;
}

.feature-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4rpx;
}

.feature-desc {
  font-size: 24rpx;
  color: #6b7280;
}

/* Footer */
.footer-section {
  padding-top: 40rpx;
  text-align: center;
}

.footer-divider {
  height: 1rpx;
  background: #f3f4f6;
  margin-bottom: 24rpx;
}

.footer-text {
  font-size: 22rpx;
  color: #9ca3af;
}

/* Responsive */
@media (max-width: 640rpx) {
  .main-content {
    padding: 24rpx 24rpx 48rpx;
  }

  .icon-wrapper {
    width: 150rpx;
    height: 150rpx;
  }

  .glow-ring {
    width: 150rpx;
    height: 150rpx;
  }

  .rotating-ring {
    inset: 12rpx;
  }

  .center-icon {
    inset: 24rpx;
  }

  .icon-image {
    width: 48rpx;
    height: 48rpx;
  }

  .message-card {
    padding: 32rpx 24rpx;
  }

  .feature-item {
    padding: 20rpx 24rpx;
  }

  .card-title {
    font-size: 32rpx;
  }

  .features-title {
    font-size: 28rpx;
  }
}
</style>
